<script lang="ts">
  import JobCard from './JobCard.svelte';
  import { flip } from 'svelte/animate';
  import { ScrollArea } from '$lib/components/ui/scroll-area';
  import { Checkbox } from '$lib/components/ui/checkbox';
  import { Button } from '$lib/components/ui/button';
  import { Badge } from '$lib/components/ui/badge';
  import { MoreHorizontal, Archive, Trash2, ArrowRight } from 'lucide-svelte';
  import * as DropdownMenu from '$lib/components/ui/dropdown-menu';
  let {
    columns,
    groupedApplications,
    openApplicationDetails,
    onFinalize = (_columnId: string, _items: any[]) => {},
    selectedItems = new Set(),
    onSelectionChange = () => {},
    columnVisibility = {},
    onBulkMove = (_targetStatus: string, _selectedIds: string[]) => {},
  }: {
    columns: any[];
    groupedApplications: Record<string, any[]>;
    openApplicationDetails: (application: any) => void;
    onFinalize: (columnId: string, items: any[]) => void;
    selectedItems: Set<string>;
    onSelectionChange: (itemId: string, selected: boolean) => void;
    columnVisibility: Record<string, boolean>;
    onBulkMove: (targetStatus: string, selectedIds: string[]) => void;
  } = $props();

  // Filter columns based on visibility and make sure each column has an items array
  const columnsData = $derived(
    columns
      .filter((column: any) => columnVisibility[column.id] !== false)
      .map((column: any) => ({
        ...column,
        items: [...(groupedApplications[column.id] || [])].map((item: any) => ({
          ...item,
          columnId: column.id, // Add columnId to each item for tracking
        })),
      }))
  );

  // Get all items across all columns
  const allItems = $derived(columnsData.flatMap((column: any) => column.items));

  // Check if all items are selected
  const allSelected = $derived(
    allItems.length > 0 && allItems.every((item: any) => selectedItems.has(item.id.toString()))
  );

  // Check if some items are selected
  const someSelected = $derived(
    allItems.some((item: any) => selectedItems.has(item.id.toString()))
  );

  // Handle select all/none
  function handleSelectAll() {
    if (allSelected) {
      // Deselect all
      allItems.forEach((item: any) => {
        onSelectionChange(item.id.toString(), false);
      });
    } else {
      // Select all
      allItems.forEach((item: any) => {
        onSelectionChange(item.id.toString(), true);
      });
    }
  }

  // Handle column select all/none
  function handleColumnSelectAll(column: any) {
    const columnItems = column.items;
    const allColumnSelected = columnItems.every((item: any) =>
      selectedItems.has(item.id.toString())
    );

    if (allColumnSelected) {
      // Deselect all in this column
      columnItems.forEach((item: any) => {
        onSelectionChange(item.id.toString(), false);
      });
    } else {
      // Select all in this column
      columnItems.forEach((item: any) => {
        onSelectionChange(item.id.toString(), true);
      });
    }
  }

  // Check if all items in a column are selected
  function isColumnAllSelected(column: any) {
    return (
      column.items.length > 0 &&
      column.items.every((item: any) => selectedItems.has(item.id.toString()))
    );
  }

  // Check if some items in a column are selected
  function isColumnSomeSelected(column: any) {
    return column.items.some((item: any) => selectedItems.has(item.id.toString()));
  }

  const flipDurationMs = 300;
  // Flip animation duration
</script>

<!-- Header with bulk actions -->
{#if selectedItems.size > 0}
  <div class="bg-muted/50 border-b p-4">
    <div class="flex items-center justify-between">
      <div class="flex items-center gap-3">
        <Checkbox
          checked={allSelected}
          indeterminate={someSelected && !allSelected}
          onCheckedChange={handleSelectAll}
          aria-label="Select all applications" />
        <Badge variant="secondary">
          {selectedItems.size} selected
        </Badge>
      </div>

      <div class="flex items-center gap-2">
        <DropdownMenu.Root>
          <DropdownMenu.Trigger>
            <Button variant="outline" size="sm">
              <MoreHorizontal class="mr-2 h-4 w-4" />
              Actions
            </Button>
          </DropdownMenu.Trigger>
          <DropdownMenu.Content>
            <DropdownMenu.Label>Move Selected To</DropdownMenu.Label>
            <DropdownMenu.Separator />
            {#each columns as targetColumn}
              <DropdownMenu.Item
                onclick={() => {
                  const selectedIds = Array.from(selectedItems);
                  onBulkMove(targetColumn.id, selectedIds);
                }}>
                <ArrowRight class="mr-2 h-4 w-4" />
                {targetColumn.name}
              </DropdownMenu.Item>
            {/each}
            <DropdownMenu.Separator />
            <DropdownMenu.Item>
              <Archive class="mr-2 h-4 w-4" />
              Archive Selected
            </DropdownMenu.Item>
            <DropdownMenu.Item class="text-destructive">
              <Trash2 class="mr-2 h-4 w-4" />
              Delete Selected
            </DropdownMenu.Item>
          </DropdownMenu.Content>
        </DropdownMenu.Root>
      </div>
    </div>
  </div>
{/if}

<ScrollArea orientation="horizontal" class="w-full">
  <div class="flex min-w-max gap-4 p-4">
    {#each columnsData as column (column.id)}
      <div
        class="bg-card flex w-80 flex-col rounded-lg border"
        role="region"
        aria-label={`Column ${column.name}`}>
        <div class="border-b p-4">
          <div class="flex items-center justify-between">
            <div class="flex items-center gap-2">
              <Checkbox
                checked={isColumnAllSelected(column)}
                indeterminate={isColumnSomeSelected(column) && !isColumnAllSelected(column)}
                onCheckedChange={() => handleColumnSelectAll(column)}
                aria-label="Select all in {column.name}" />
              <h3 class="text-sm font-semibold uppercase tracking-wide">
                {column.name}
              </h3>
            </div>
            <Badge variant="secondary" class="text-xs">
              {column.items.length}
            </Badge>
          </div>
        </div>

        <div class="flex-1 overflow-y-auto p-4">
          <section
            aria-label="Drop zone for {column.name}"
            class="min-h-[400px] space-y-3"
            ondragover={(e) => e.preventDefault()}
            ondrop={(e) => {
              e.preventDefault();
              const itemId = e.dataTransfer.getData('text/plain');
              if (itemId) {
                // Find the item in all columns
                let foundItem = null;
                let sourceColumnId = null;

                // Search through all columns to find the item
                for (const col of columnsData) {
                  const item = col.items.find((i: any) => i.id.toString() === itemId);
                  if (item) {
                    foundItem = { ...item };
                    sourceColumnId = col.id;
                    break;
                  }
                }

                if (foundItem && sourceColumnId !== column.id) {
                  // Update the item's status
                  foundItem.status = column.id;

                  // Call onFinalize callback with the updated item
                  onFinalize(column.id, [foundItem]);
                }
              }
            }}>
            {#each column.items as item (item.id)}
              <div animate:flip={{ duration: flipDurationMs }}>
                <JobCard
                  application={item}
                  {openApplicationDetails}
                  isSelected={selectedItems.has(item.id.toString())}
                  onSelectionChange={(selected) =>
                    onSelectionChange(item.id.toString(), selected)} />
              </div>
            {/each}
          </section>
        </div>
      </div>
    {/each}
  </div>
</ScrollArea>
