<script lang="ts">
  import JobCard from './JobCard.svelte';
  import { flip } from 'svelte/animate';
  import { ScrollArea } from '$lib/components/ui/scroll-area';
  export let columns: any[];
  export let groupedApplications: Record<string, any[]>;
  export let openApplicationDetails: (application: any) => void;
  export let onFinalize = (_columnId: string, _items: any[]) => {};
  export let selectedItems: Set<string> = new Set();
  export let onSelectionChange: (itemId: string, selected: boolean) => void = () => {};
  export let columnVisibility: Record<string, boolean> = {};

  // Filter columns based on visibility and make sure each column has an items array
  $: columnsData = columns
    .filter((column: any) => columnVisibility[column.id] !== false)
    .map((column: any) => ({
      ...column,
      items: [...(groupedApplications[column.id] || [])].map((item: any) => ({
        ...item,
        columnId: column.id, // Add columnId to each item for tracking
      })),
    }));

  const flipDurationMs = 300;
  // Flip animation duration
</script>

<ScrollArea orientation="horizontal" class="w-full">
  <div class="flex min-w-max gap-4 p-4">
    {#each columnsData as column (column.id)}
      <div
        class="bg-card flex w-80 flex-col rounded-lg border"
        role="region"
        aria-label={`Column ${column.name}`}>
        <h3 class="border-b p-4 text-center font-semibold">
          {column.name} ({column.items.length})
        </h3>

        <div class="flex-1 overflow-y-auto p-4">
          <section
            aria-label="Drop zone for {column.name}"
            class="min-h-[400px] space-y-3"
            on:dragover|preventDefault
            on:drop|preventDefault={(e) => {
              const itemId = e.dataTransfer.getData('text/plain');
              if (itemId) {
                // Find the item in all columns
                let foundItem = null;
                let sourceColumnId = null;

                // Search through all columns to find the item
                for (const col of columnsData) {
                  const item = col.items.find((i: any) => i.id.toString() === itemId);
                  if (item) {
                    foundItem = { ...item };
                    sourceColumnId = col.id;
                    break;
                  }
                }

                if (foundItem && sourceColumnId !== column.id) {
                  // Update the item's status
                  foundItem.status = column.id;

                  // Remove from source column and add to target column
                  columnsData = columnsData.map((col: any) => {
                    if (col.id === sourceColumnId) {
                      return {
                        ...col,
                        items: col.items.filter((i: any) => i.id.toString() !== itemId),
                      };
                    } else if (col.id === column.id) {
                      return {
                        ...col,
                        items: [...col.items, foundItem],
                      };
                    }
                    return col;
                  });

                  // Call onFinalize callback
                  onFinalize(column.id, columnsData.find((c: any) => c.id === column.id).items);
                }
              }
            }}>
            {#each column.items as item (item.id)}
              <div animate:flip={{ duration: flipDurationMs }}>
                <JobCard
                  application={item}
                  {openApplicationDetails}
                  isSelected={selectedItems.has(item.id.toString())}
                  onSelectionChange={(selected) =>
                    onSelectionChange(item.id.toString(), selected)} />
              </div>
            {/each}
          </section>
        </div>
      </div>
    {/each}
  </div>
</ScrollArea>
