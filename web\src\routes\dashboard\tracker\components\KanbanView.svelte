<script lang="ts">
  import JobCard from './JobCard.svelte';
  import { flip } from 'svelte/animate';
  import { ScrollArea } from '$lib/components/ui/scroll-area';
  let {
    columns,
    groupedApplications,
    openApplicationDetails,
    onFinalize = (_columnId: string, _items: any[]) => {},
    selectedItems = new Set(),
    onSelectionChange = () => {},
    columnVisibility = {},
  }: {
    columns: any[];
    groupedApplications: Record<string, any[]>;
    openApplicationDetails: (application: any) => void;
    onFinalize: (columnId: string, items: any[]) => void;
    selectedItems: Set<string>;
    onSelectionChange: (itemId: string, selected: boolean) => void;
    columnVisibility: Record<string, boolean>;
  } = $props();

  // Filter columns based on visibility and make sure each column has an items array
  const columnsData = $derived(
    columns
      .filter((column: any) => columnVisibility[column.id] !== false)
      .map((column: any) => ({
        ...column,
        items: [...(groupedApplications[column.id] || [])].map((item: any) => ({
          ...item,
          columnId: column.id, // Add columnId to each item for tracking
        })),
      }))
  );

  const flipDurationMs = 300;
  // Flip animation duration
</script>

<ScrollArea orientation="horizontal" class="w-full">
  <div class="flex min-w-max gap-4 p-4">
    {#each columnsData as column (column.id)}
      <div
        class="bg-card flex w-80 flex-col rounded-lg border"
        role="region"
        aria-label={`Column ${column.name}`}>
        <h3 class="border-b p-4 text-center text-sm font-semibold uppercase tracking-wide">
          {column.name} ({column.items.length})
        </h3>

        <div class="flex-1 overflow-y-auto p-4">
          <section
            aria-label="Drop zone for {column.name}"
            class="min-h-[400px] space-y-3"
            ondragover={(e) => e.preventDefault()}
            ondrop={(e) => {
              e.preventDefault();
              const itemId = e.dataTransfer.getData('text/plain');
              if (itemId) {
                // Find the item in all columns
                let foundItem = null;
                let sourceColumnId = null;

                // Search through all columns to find the item
                for (const col of columnsData) {
                  const item = col.items.find((i: any) => i.id.toString() === itemId);
                  if (item) {
                    foundItem = { ...item };
                    sourceColumnId = col.id;
                    break;
                  }
                }

                if (foundItem && sourceColumnId !== column.id) {
                  // Update the item's status
                  foundItem.status = column.id;

                  // Call onFinalize callback with the updated item
                  onFinalize(column.id, [foundItem]);
                }
              }
            }}>
            {#each column.items as item (item.id)}
              <div animate:flip={{ duration: flipDurationMs }}>
                <JobCard
                  application={item}
                  {openApplicationDetails}
                  isSelected={selectedItems.has(item.id.toString())}
                  onSelectionChange={(selected) =>
                    onSelectionChange(item.id.toString(), selected)} />
              </div>
            {/each}
          </section>
        </div>
      </div>
    {/each}
  </div>
</ScrollArea>
