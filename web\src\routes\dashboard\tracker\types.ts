import {
  Clock,
  Calendar,
  AlertCircle,
  XCircle,
  CheckCircle2,
  MapPin,
  Briefcase,
  FileText,
  Bookmark,
} from 'lucide-svelte';
import { z } from 'zod';

export const jobApplicationSchema = z.object({
  id: z.number(),
  company: z.string(),
  position: z.string(),
  location: z.string(),
  appliedDate: z.string(),
  status: z.string(),
  nextAction: z.string().optional(),
  notes: z.string().optional(),
  logo: z.string().optional(),
  url: z.string().optional(),
  jobType: z.string(),
  resumeUploaded: z.string(),
});

export type JobApplication = z.infer<typeof jobApplicationSchema>;

// Job status types
export const statuses = [
  { id: 'Saved', name: 'Saved', icon: Bookmark },
  { id: 'Applied', name: 'Applied', icon: Clock },
  { id: 'Interview', name: 'Interview', icon: Calendar },
  { id: 'Assessment', name: 'Assessment', icon: AlertCircle },
  { id: 'Rejected', name: 'Rejected', icon: XCircle },
  { id: 'Offer', name: 'Offer', icon: CheckCircle2 },
];

// Job type options
export const jobTypes = [
  { id: 'Full-time', name: 'Full-time', icon: Briefcase },
  { id: 'Part-time', name: 'Part-time', icon: Briefcase },
  { id: 'Contract', name: 'Contract', icon: FileText },
  { id: 'Internship', name: 'Internship', icon: Briefcase },
  { id: 'Freelance', name: 'Freelance', icon: Briefcase },
];

// Location types (for filtering)
export const locations = [
  { id: 'Remote', name: 'Remote', icon: MapPin },
  { id: 'Hybrid', name: 'Hybrid', icon: MapPin },
  { id: 'On-site', name: 'On-site', icon: MapPin },
];

// Resume uploaded options
export const resumeOptions = [
  { id: 'Yes', name: 'Yes', icon: FileText },
  { id: 'No', name: 'No', icon: XCircle },
  { id: 'N/A', name: 'N/A', icon: AlertCircle },
];

// Status colors for badges
export const statusColors = {
  Saved: 'bg-gray-100 text-gray-800',
  Applied: 'bg-blue-100 text-blue-800',
  Interview: 'bg-purple-100 text-purple-800',
  Assessment: 'bg-yellow-100 text-yellow-800',
  Rejected: 'bg-red-100 text-red-800',
  Offer: 'bg-green-100 text-green-800',
};

// Status icons
export const statusIcons = {
  Saved: Bookmark,
  Applied: Clock,
  Interview: Calendar,
  Assessment: AlertCircle,
  Rejected: XCircle,
  Offer: CheckCircle2,
};
