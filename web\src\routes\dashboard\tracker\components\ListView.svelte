<script lang="ts">
  import JobCard from './JobCard.svelte';
  import { ScrollArea } from '$lib/components/ui/scroll-area';
  import { flip } from 'svelte/animate';

  let {
    filteredApplications = [],
    openApplicationDetails,
    selectedItems = new Set(),
    onSelectionChange = () => {},
  }: {
    filteredApplications: any[];
    openApplicationDetails: (application: any) => void;
    selectedItems: Set<string>;
    onSelectionChange: (itemId: string, selected: boolean) => void;
  } = $props();

  const flipDurationMs = 300;
</script>

<ScrollArea class="h-[600px] w-full">
  <div class="space-y-3 p-4">
    {#if filteredApplications.length > 0}
      {#each filteredApplications as application (application.id)}
        <div animate:flip={{ duration: flipDurationMs }}>
          <JobCard
            {application}
            {openApplicationDetails}
            isSelected={selectedItems.has(application.id.toString())}
            onSelectionChange={(selected) =>
              onSelectionChange(application.id.toString(), selected)} />
        </div>
      {/each}
    {:else}
      <div class="flex h-32 items-center justify-center text-center">
        <div>
          <p class="text-muted-foreground">No applications found</p>
          <p class="text-muted-foreground text-sm">Try adjusting your filters</p>
        </div>
      </div>
    {/if}
  </div>
</ScrollArea>
