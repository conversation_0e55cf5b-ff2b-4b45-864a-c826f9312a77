<script lang="ts">
  import { Checkbox } from '$lib/components/ui/checkbox';
  import { Button } from '$lib/components/ui/button';
  import { Badge } from '$lib/components/ui/badge';
  import { MoreHorizontal, MapPin } from 'lucide-svelte';
  import { statusColors, statusIcons } from '../types';

  export let filteredApplications: any[] = [];
  export let openApplicationDetails: (application: any) => void;
  export let selectedItems: Set<string> = new Set();
  export let onSelectionChange: (itemId: string, selected: boolean) => void = () => {};

  function formatDate(dateStr: string) {
    if (!dateStr) return 'N/A';
    const date = new Date(dateStr);
    if (isNaN(date.getTime())) return 'Invalid date';
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    }).format(date);
  }
</script>

<div class="rounded-md border">
  <!-- Header -->
  <div class="bg-muted/50 border-b px-4 py-3">
    <div class="text-muted-foreground grid grid-cols-12 gap-4 text-sm font-medium">
      <div class="col-span-1">
        <Checkbox />
      </div>
      <div class="col-span-3">Company</div>
      <div class="col-span-2">Position</div>
      <div class="col-span-2">Location</div>
      <div class="col-span-2">Applied</div>
      <div class="col-span-1">Status</div>
      <div class="col-span-1"></div>
    </div>
  </div>

  <!-- Body -->
  <div class="divide-y">
    {#if filteredApplications.length > 0}
      {#each filteredApplications as application (application.id)}
        <div
          class="hover:bg-muted/50 grid cursor-pointer grid-cols-12 gap-4 px-4 py-4 transition-colors"
          onclick={() => openApplicationDetails(application)}>
          <!-- Checkbox -->
          <div class="col-span-1 flex items-center">
            <Checkbox
              checked={selectedItems.has(application.id.toString())}
              onCheckedChange={(checked) => onSelectionChange(application.id.toString(), !!checked)}
              onclick={(e) => e.stopPropagation()} />
          </div>

          <!-- Company with Logo -->
          <div class="col-span-3 flex items-center gap-3">
            <div class="bg-muted h-10 w-10 flex-shrink-0 overflow-hidden rounded-lg">
              <img
                src={application.logo}
                alt={application.company}
                class="h-full w-full object-cover" />
            </div>
            <div class="min-w-0">
              <div class="truncate font-medium">{application.company}</div>
              <div class="text-muted-foreground truncate text-sm">{application.jobType}</div>
            </div>
          </div>

          <!-- Position -->
          <div class="col-span-2 flex items-center">
            <div class="min-w-0">
              <div class="truncate font-medium">{application.position}</div>
            </div>
          </div>

          <!-- Location -->
          <div class="col-span-2 flex items-center">
            <div class="text-muted-foreground flex items-center gap-1 text-sm">
              <MapPin class="h-3 w-3 flex-shrink-0" />
              <span class="truncate">{application.location}</span>
            </div>
          </div>

          <!-- Applied Date -->
          <div class="col-span-2 flex items-center">
            <div class="text-sm">{formatDate(application.appliedDate)}</div>
          </div>

          <!-- Status -->
          <div class="col-span-1 flex items-center">
            <Badge
              class={`${statusColors[application.status] || 'bg-gray-100 text-gray-800'} flex items-center gap-1`}>
              {#if statusIcons[application.status]}
                {@const IconComponent = statusIcons[application.status]}
                <IconComponent class="h-3 w-3" />
              {/if}
              {application.status}
            </Badge>
          </div>

          <!-- Actions -->
          <div class="col-span-1 flex items-center justify-end">
            <Button variant="ghost" size="icon" class="h-8 w-8">
              <MoreHorizontal class="h-4 w-4" />
            </Button>
          </div>
        </div>
      {/each}
    {:else}
      <div class="flex h-32 items-center justify-center text-center">
        <div>
          <p class="text-muted-foreground">No applications found</p>
          <p class="text-muted-foreground text-sm">Try adjusting your filters</p>
        </div>
      </div>
    {/if}
  </div>
</div>
