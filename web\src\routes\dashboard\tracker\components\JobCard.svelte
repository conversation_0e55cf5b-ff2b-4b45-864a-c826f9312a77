<script lang="ts">
  import { Building, Calendar, Clock, MapPin, MoreHorizontal } from 'lucide-svelte';
  import { Button } from '$lib/components/ui/button';
  import { Card } from '$lib/components/ui/card';
  import { Checkbox } from '$lib/components/ui/checkbox';

  export let application: any;
  export let openApplicationDetails: (application: any) => void;
  export let isSelected = false;
  export let onSelectionChange: (selected: boolean) => void = () => {};

  $: cardClass = `mb-3 cursor-pointer p-4 hover:shadow-md ${isSelected ? 'ring-2 ring-primary' : ''}`;
</script>

<style>
  :global(.dragging) {
    opacity: 0.6;
    transform: scale(0.95);
  }
</style>

<div
  role="button"
  tabindex="0"
  draggable="true"
  ondragstart={(e) => {
    e.dataTransfer.setData('text/plain', application.id.toString());
    e.dataTransfer.effectAllowed = 'move';
    // Add a class to the element being dragged
    e.currentTarget.classList.add('dragging');
  }}
  ondragend={(e) => {
    // Remove the class when dragging ends
    e.currentTarget.classList.remove('dragging');
  }}
  onclick={() => openApplicationDetails(application)}
  onkeydown={(e) => e.key === 'Enter' && openApplicationDetails(application)}
  aria-label={`${application.position} at ${application.company}`}
  class="w-full text-left">
  <Card class={cardClass}>
    <div class="flex items-start gap-3">
      <!-- Selection Checkbox -->
      <div class="flex-shrink-0 pt-1">
        <Checkbox
          checked={isSelected}
          onCheckedChange={(checked) => onSelectionChange(!!checked)}
          onclick={(e) => e.stopPropagation()}
          aria-label="Select application" />
      </div>

      <div class="bg-muted h-10 w-10 flex-shrink-0 overflow-hidden rounded-full">
        <img src={application.logo} alt={application.company} class="h-full w-full object-cover" />
      </div>
      <div class="flex-1 overflow-hidden">
        <h3 class="truncate font-semibold">{application.position}</h3>
        <div class="text-muted-foreground flex items-center text-sm">
          <Building class="mr-1 h-3 w-3" />
          <span class="truncate">{application.company}</span>
        </div>
        <div class="text-muted-foreground flex items-center text-sm">
          <MapPin class="mr-1 h-3 w-3" />
          <span class="truncate">{application.location}</span>
        </div>
        {#if application.url}
          <div class="mt-1">
            <a
              href={application.url}
              target="_blank"
              rel="noopener noreferrer"
              class="text-primary text-xs hover:underline"
              onclick={(e) => e.stopPropagation()}>
              View Job Posting
            </a>
          </div>
        {/if}
      </div>
      <Button variant="ghost" size="icon" class="h-8 w-8 flex-shrink-0">
        <MoreHorizontal class="h-4 w-4" />
      </Button>
    </div>
    <div class="text-muted-foreground mt-2 flex items-center justify-between text-xs">
      <div class="flex items-center">
        <Calendar class="mr-1 h-3 w-3" />
        <span>Applied {application.appliedDate}</span>
      </div>
      <div class="flex items-center">
        <Clock class="mr-1 h-3 w-3" />
        <span>{application.nextAction}</span>
      </div>
    </div>
  </Card>
</div>
