<script lang="ts">
  import { Building, MapPin, MoreHorizontal } from 'lucide-svelte';
  import { Button } from '$lib/components/ui/button';
  import { Card } from '$lib/components/ui/card';
  import { Checkbox } from '$lib/components/ui/checkbox';
  import { Badge } from '$lib/components/ui/badge';
  import { statusColors, statusIcons } from '../types';

  export let application: any;
  export let openApplicationDetails: (application: any) => void;
  export let isSelected = false;
  export let onSelectionChange: (selected: boolean) => void = () => {};

  $: cardClass = `cursor-pointer p-4 hover:shadow-md transition-all ${isSelected ? 'ring-2 ring-primary' : ''}`;
</script>

<style>
  :global(.dragging) {
    opacity: 0.6;
    transform: scale(0.95);
  }
</style>

<div
  role="button"
  tabindex="0"
  draggable="true"
  ondragstart={(e) => {
    e.dataTransfer.setData('text/plain', application.id.toString());
    e.dataTransfer.effectAllowed = 'move';
    // Add a class to the element being dragged
    e.currentTarget.classList.add('dragging');
  }}
  ondragend={(e) => {
    // Remove the class when dragging ends
    e.currentTarget.classList.remove('dragging');
  }}
  onclick={() => openApplicationDetails(application)}
  onkeydown={(e) => e.key === 'Enter' && openApplicationDetails(application)}
  aria-label={`${application.position} at ${application.company}`}
  class="w-full text-left">
  <Card class={cardClass}>
    <div class="flex items-center gap-4">
      <!-- Selection Checkbox -->
      <div class="flex-shrink-0">
        <Checkbox
          checked={isSelected}
          onCheckedChange={(checked) => onSelectionChange(!!checked)}
          onclick={(e) => e.stopPropagation()}
          aria-label="Select application" />
      </div>

      <!-- Company Logo -->
      <div class="bg-muted h-12 w-12 flex-shrink-0 overflow-hidden rounded-lg">
        <img src={application.logo} alt={application.company} class="h-full w-full object-cover" />
      </div>

      <!-- Job Details -->
      <div class="min-w-0 flex-1">
        <h3 class="truncate text-base font-semibold">{application.position}</h3>
        <div class="text-muted-foreground mt-1 flex items-center text-sm">
          <Building class="mr-1 h-3 w-3 flex-shrink-0" />
          <span class="truncate">{application.company}</span>
        </div>
        <div class="text-muted-foreground flex items-center text-sm">
          <MapPin class="mr-1 h-3 w-3 flex-shrink-0" />
          <span class="truncate">{application.location}</span>
        </div>
      </div>

      <!-- Status Columns -->
      <div class="flex items-center gap-8 text-center">
        <!-- Applied Date -->
        <div class="min-w-[80px]">
          <div class="text-muted-foreground text-xs">Applied</div>
          <div class="text-sm font-medium">{application.appliedDate}</div>
        </div>

        <!-- Status -->
        <div class="min-w-[100px]">
          <div class="text-muted-foreground mb-1 text-xs">Status</div>
          <Badge
            class={`${statusColors[application.status] || 'bg-gray-100 text-gray-800'} flex items-center gap-1`}>
            {#if statusIcons[application.status]}
              {@const IconComponent = statusIcons[application.status]}
              <IconComponent class="h-3 w-3" />
            {/if}
            {application.status}
          </Badge>
        </div>

        <!-- Actions -->
        <div class="flex-shrink-0">
          <Button variant="ghost" size="icon" class="h-8 w-8">
            <MoreHorizontal class="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  </Card>
</div>
