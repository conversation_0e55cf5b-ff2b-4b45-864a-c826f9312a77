<script lang="ts">
  import * as DropdownMenu from '$lib/components/ui/dropdown-menu';
  import { Button } from '$lib/components/ui/button';
  import { Columns } from 'lucide-svelte';

  let {
    tableModel = null,
    viewMode = 'list',
    kanbanColumnVisibility = $bindable(),
  }: {
    tableModel: any;
    viewMode: string;
    kanbanColumnVisibility: Record<string, boolean>;
  } = $props();

  // Define columns that can be toggled for table view
  const tableColumns = [
    { id: 'company', label: 'Company' },
    { id: 'position', label: 'Position' },
    { id: 'status', label: 'Status' },
    { id: 'jobType', label: 'Job Type' },
    { id: 'location', label: 'Location' },
    { id: 'appliedDate', label: 'Applied Date' },
    { id: 'nextAction', label: 'Next Action' },
  ];

  // Define Kanban columns (status columns)
  const kanbanColumns = [
    { id: 'Saved', label: 'Saved' },
    { id: 'Applied', label: 'Applied' },
    { id: 'Phone Screen', label: 'Phone Screen' },
    { id: 'Interview', label: 'Interview' },
    { id: 'Assessment', label: 'Assessment' },
    { id: 'Final Round', label: 'Final Round' },
    { id: 'Offer', label: 'Offer' },
    { id: 'Accepted', label: 'Accepted' },
    { id: 'Rejected', label: 'Rejected' },
  ];

  // Use appropriate columns based on view mode
  const columns = $derived(viewMode === 'kanban' ? kanbanColumns : tableColumns);

  // Function to toggle column visibility
  function toggleColumnVisibility(columnId: string) {
    if (viewMode === 'kanban') {
      // For Kanban view, toggle our local state
      kanbanColumnVisibility[columnId] = !kanbanColumnVisibility[columnId];
      kanbanColumnVisibility = { ...kanbanColumnVisibility }; // Trigger reactivity
    } else if (tableModel) {
      // For table view, use the table model
      const column = tableModel.getAllColumns?.()?.find((col: any) => col.id === columnId);
      if (column?.toggleVisibility) {
        column.toggleVisibility();
      }
    }
  }

  // Function to check if a column is visible
  function isColumnVisible(columnId: string): boolean {
    if (viewMode === 'kanban') {
      return kanbanColumnVisibility[columnId] ?? true;
    } else if (tableModel) {
      const column = tableModel.getAllColumns?.()?.find((col: any) => col.id === columnId);
      if (column?.getIsVisible) {
        return column.getIsVisible();
      }
    }
    return true;
  }
</script>

<DropdownMenu.Root>
  <DropdownMenu.Trigger>
    <Button variant="outline" size="sm" class="h-8">
      <Columns class="mr-2 h-4 w-4" />
      Columns
    </Button>
  </DropdownMenu.Trigger>
  <DropdownMenu.Content align="end" class="w-[150px]">
    <DropdownMenu.Label>
      {viewMode === 'kanban' ? 'Toggle status columns' : 'Toggle columns'}
    </DropdownMenu.Label>
    <DropdownMenu.Separator />
    {#each columns as column}
      <DropdownMenu.Item onclick={() => toggleColumnVisibility(column.id)}>
        <div class="flex items-center">
          <div class="border-primary mr-2 flex h-4 w-4 items-center justify-center rounded border">
            {#if isColumnVisible(column.id)}
              <div class="bg-primary h-2 w-2 rounded-sm"></div>
            {/if}
          </div>
          <span>{column.label}</span>
        </div>
      </DropdownMenu.Item>
    {/each}
  </DropdownMenu.Content>
</DropdownMenu.Root>
