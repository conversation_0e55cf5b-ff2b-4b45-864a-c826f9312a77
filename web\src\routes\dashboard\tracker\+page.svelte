<script lang="ts">
  import { superForm } from 'sveltekit-superforms/client';
  import { zodClient } from 'sveltekit-superforms/adapters';
  import { toast } from 'svelte-sonner';
  import { jobApplicationSchema } from '$lib/validators/jobApplication';
  import {
    List,
    LayoutGrid,
    Plus,
    Search,
    Briefcase,
    CheckCircle,
    Download,
    Upload,
    Calendar as CalendarIcon,
  } from 'lucide-svelte';
  import { Badge } from '$lib/components/ui/badge';
  import { Button } from '$lib/components/ui/button';
  import * as Tabs from '$lib/components/ui/tabs';
  import * as Select from '$lib/components/ui/select';
  import * as Popover from '$lib/components/ui/popover';
  import * as RangeCalendar from '$lib/components/ui/range-calendar';
  import SEO from '$components/shared/SEO.svelte';
  import AddJobModal from './components/AddJobModal.svelte';
  import KanbanView from './components/KanbanView.svelte';
  import ColumnVisibility from './components/ColumnVisibility.svelte';
  import ApplicationDetailsSheet from './components/ApplicationDetailsSheet.svelte';
  import { Input } from '$lib/components/ui/input';
  import { browser } from '$app/environment';
  import DataTable from './data-table-new.svelte';
  import { DateFormatter, getLocalTimeZone, today } from '@internationalized/date';

  import { statusColors } from './types';

  export let data;

  // Set up the form with validation
  const { form, enhance, reset, errors, constraints, submitting } = superForm(data.form, {
    validators: zodClient(jobApplicationSchema),
    dataType: 'json',
    resetForm: true,
    onResult: ({ result }) => {
      if (result.type === 'success') {
        // Add the new application to the list
        const newApplication = {
          id: result.data?.application?.id || crypto.randomUUID(),
          company: $form.company || '',
          position: $form.position || '',
          location: $form.location || 'Remote',
          appliedDate: $form.appliedDate || '',
          status: $form.status || 'Applied',
          nextAction: $form.nextAction || '',
          notes: $form.notes || '',
          logo: 'https://placehold.co/100x100',
          url: $form.url || '',
          jobType: $form.jobType || 'Full-time',
          resumeUploaded: $form.resumeUploaded || 'No',
        };

        // Update applications array with the new job
        applications = [...applications, newApplication];

        // Close modal and show success message
        newJobModalOpen = false;
        toast.success('New job application added successfully!');
      } else if (result.type === 'failure') {
        toast.error('Failed to add job application. Please try again.');
      }
    },
  });

  console.log(data);

  // Add state for the sheet
  let sheetOpen = false;
  let selectedApplication = null;

  // Add state for the new job modal
  let newJobModalOpen = false;

  // Job type options
  const jobTypes = ['Full-time', 'Part-time', 'Contract', 'Freelance', 'Internship'];

  // Job status options
  const jobStatuses = ['Applied', 'Interview', 'Assessment', 'Offer', 'Rejected'];

  // Function to open the sheet with the selected application
  function openApplicationDetails(application: any) {
    selectedApplication = application;
    sheetOpen = true;
  }

  // Use the applications from the server
  let applications: any[] = data.applications || [];

  let activeView = true; // true for active, false for archived
  let viewMode = 'kanban'; // 'kanban' or 'list'

  // Selection state for Kanban view
  let selectedKanbanItems = new Set<string>();

  // Filter state
  let currentFilters = {
    appliedFromDate: '',
    appliedUntilDate: '',
    jobType: '',
    status: '',
    searchTerm: '',
  };

  // Select component values
  let selectedJobType = '';
  let selectedStatus = '';

  // Date range state for the range calendar
  let dateRange: any = undefined;
  let dateRangeOpen = false;

  // Date formatter for display
  const df = new DateFormatter('en-US', {
    dateStyle: 'medium',
  });

  // Update filters when select values change
  $: currentFilters.jobType = selectedJobType;
  $: currentFilters.status = selectedStatus;

  // Update date filters when range changes
  $: if (dateRange?.start && dateRange?.end) {
    currentFilters.appliedFromDate = dateRange.start.toString();
    currentFilters.appliedUntilDate = dateRange.end.toString();
  } else if (dateRange?.start) {
    currentFilters.appliedFromDate = dateRange.start.toString();
    currentFilters.appliedUntilDate = '';
  } else {
    currentFilters.appliedFromDate = '';
    currentFilters.appliedUntilDate = '';
  }

  // Table model for column visibility (will be set by DataTable component)
  let tableModel: any = null;

  // Kanban column visibility state
  let kanbanColumnVisibility = {
    Applied: true,
    Interview: true,
    Assessment: true,
    Offer: true,
    Rejected: true,
  };

  // Status colors and icons are now imported from types.ts

  // Define the columns for our Kanban board
  const columns = [
    { id: 'Applied', name: 'Applied' },
    { id: 'Interview', name: 'Interview' },
    { id: 'Assessment', name: 'Assessment' },
    { id: 'Offer', name: 'Offer' },
    { id: 'Rejected', name: 'Rejected' },
  ];

  // Filter applications based on active/archived view and all filters
  $: filteredApplications = applications.filter((app) => {
    const isArchived = app.status === 'Rejected' || app.status === 'Offer';
    const matchesView = activeView ? !isArchived : isArchived;

    // Search filter
    const matchesSearch =
      currentFilters.searchTerm === '' ||
      app.position.toLowerCase().includes(currentFilters.searchTerm.toLowerCase()) ||
      app.company.toLowerCase().includes(currentFilters.searchTerm.toLowerCase());

    // Job type filter
    const matchesJobType = currentFilters.jobType === '' || app.jobType === currentFilters.jobType;

    // Status filter
    const matchesStatus = currentFilters.status === '' || app.status === currentFilters.status;

    // Date filters
    const matchesFromDate =
      currentFilters.appliedFromDate === '' ||
      new Date(app.appliedDate) >= new Date(currentFilters.appliedFromDate);

    const matchesToDate =
      currentFilters.appliedUntilDate === '' ||
      new Date(app.appliedDate) <= new Date(currentFilters.appliedUntilDate);

    return (
      matchesView &&
      matchesSearch &&
      matchesJobType &&
      matchesStatus &&
      matchesFromDate &&
      matchesToDate
    );
  });

  // Group applications by status for Kanban view
  $: groupedApplications = columns.reduce((acc, column) => {
    acc[column.id] = filteredApplications.filter((app) => app.status === column.id);
    return acc;
  }, {});

  // Add some CSS for drag and drop visual feedback, but only in the browser
  $: if (browser) {
    // This will only run in the browser, not during SSR
    const dragStyle = document.createElement('style');
    dragStyle.textContent = `
      .dragging {
        opacity: 0.7;
        transform: scale(0.95);
        box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
      }
    `;
    document.head.appendChild(dragStyle);
  }

  // List view now uses DataTable component instead of ListView

  function handleKanbanFinalize(event: CustomEvent) {
    const { columnId, items } = event.detail;

    // Find the items that were moved to this column
    const movedItems = items.filter((item: any) => !item.columnId || item.columnId !== columnId);

    // Update the status of moved items in the applications array
    if (movedItems.length > 0) {
      applications = applications.map((app) => {
        const movedItem = movedItems.find((item: any) => item.id === app.id);
        if (movedItem) {
          // Update the status and show a toast notification
          toast.success(`Moved "${app.position}" to ${columnId}`);
          return { ...app, status: columnId };
        }
        return app;
      });

      // Update the grouped applications for all columns
      groupedApplications = columns.reduce((acc, column) => {
        acc[column.id] = applications.filter((app) => app.status === column.id);
        return acc;
      }, {});
    }
  }

  // Handle Kanban selection changes
  function handleKanbanSelectionChange(itemId: string, selected: boolean) {
    if (selected) {
      selectedKanbanItems.add(itemId);
    } else {
      selectedKanbanItems.delete(itemId);
    }
    selectedKanbanItems = new Set(selectedKanbanItems); // Trigger reactivity
  }

  // Export CSV functionality
  let isExporting = false;
  async function exportCSV() {
    if (isExporting) return;
    isExporting = true;

    try {
      const headers = [
        'Company',
        'Position',
        'Location',
        'Applied Date',
        'Status',
        'Job Type',
        'Next Action',
      ];
      const csvContent = [
        headers.join(','),
        ...filteredApplications.map((app) =>
          [
            `"${app.company?.replace(/"/g, '""') || ''}"`,
            `"${app.position?.replace(/"/g, '""') || ''}"`,
            `"${app.location?.replace(/"/g, '""') || ''}"`,
            `"${app.appliedDate?.replace(/"/g, '""') || ''}"`,
            `"${app.status?.replace(/"/g, '""') || ''}"`,
            `"${app.jobType?.replace(/"/g, '""') || ''}"`,
            `"${app.nextAction?.replace(/"/g, '""') || ''}"`,
          ].join(',')
        ),
      ].join('\n');

      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.setAttribute('href', url);
      link.setAttribute('download', 'job_applications.csv');
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      toast.success('CSV exported successfully!');
    } catch (error) {
      toast.error('Failed to export CSV');
    } finally {
      setTimeout(() => {
        isExporting = false;
      }, 500);
    }
  }

  // Import CSV functionality
  let isImporting = false;
  function handleImportCSV() {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.csv';
    input.onchange = (e) => {
      const file = (e.target as HTMLInputElement)?.files?.[0];
      if (file) {
        isImporting = true;
        // Here you would handle the CSV import
        setTimeout(() => {
          isImporting = false;
          toast.success('CSV imported successfully!');
        }, 1000);
      }
    };
    input.click();
  }
</script>

<SEO
  title="Job Tracker | Hirli"
  description="Track your job applications in one place. Organize, monitor, and optimize your entire job search process with our intuitive job tracker."
  keywords="job tracker, job applications, job search, application tracking, job status management, application organization" />

<!-- Tabs for different sections -->
<Tabs.Root
  value={activeView ? 'active' : 'archived'}
  onValueChange={(value) => (activeView = value === 'active')}>
  <div class="p-0">
    <Tabs.List class="border-t-0">
      <Tabs.Trigger value="active" class="flex-1 gap-2">
        <Briefcase class="h-4 w-4" />
        <span>Active Applications</span>
      </Tabs.Trigger>
      <Tabs.Trigger value="archived" class="flex-1 gap-2">
        <CheckCircle class="h-4 w-4" />
        <span>Archived</span>
      </Tabs.Trigger>
    </Tabs.List>
  </div>

  <!-- Active Applications Tab -->
  <Tabs.Content value="active" class="p-4">
    <!-- Header with actions -->
    <div class="mb-6 space-y-4">
      <!-- Top row: Results, View Toggle, and Add Button -->
      <div class="flex items-center justify-between">
        <div class="flex items-center gap-4">
          <Badge variant="secondary" class="text-sm">
            {filteredApplications.length} applications
          </Badge>

          <!-- View Mode Toggle -->
          <Tabs.Root value={viewMode} onValueChange={(value) => (viewMode = value)}>
            <Tabs.List class="h-8">
              <Tabs.Trigger value="kanban" class="h-6 px-3">
                <LayoutGrid class="h-3 w-3" />
              </Tabs.Trigger>
              <Tabs.Trigger value="list" class="h-6 px-3">
                <List class="h-3 w-3" />
              </Tabs.Trigger>
            </Tabs.List>
          </Tabs.Root>
        </div>

        <Button onclick={() => (newJobModalOpen = true)} class="flex items-center gap-2">
          <Plus class="h-4 w-4" />
          Add Application
        </Button>
      </div>

      <!-- Filters row -->
      <div class="flex items-center justify-between">
        <!-- Left side: Search and filters -->
        <div class="flex items-center gap-2">
          <!-- Search -->
          <div class="relative">
            <Search
              class="text-muted-foreground absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2" />
            <Input
              type="text"
              placeholder="Search for roles or companies..."
              class="w-64 pl-9"
              bind:value={currentFilters.searchTerm} />
          </div>

          <!-- Date Range Picker -->
          <Popover.Root bind:open={dateRangeOpen}>
            <Popover.Trigger asChild let:builder>
              <Button
                variant="outline"
                class="w-60 justify-start text-left font-normal"
                builders={[builder]}>
                <CalendarIcon class="mr-2 h-4 w-4" />
                {#if dateRange && dateRange.start}
                  {#if dateRange.end}
                    {df.format(dateRange.start.toDate(getLocalTimeZone()))} - {df.format(
                      dateRange.end.toDate(getLocalTimeZone())
                    )}
                  {:else}
                    {df.format(dateRange.start.toDate(getLocalTimeZone()))}
                  {/if}
                {:else}
                  Applied date range
                {/if}
              </Button>
            </Popover.Trigger>
            <Popover.Content class="w-auto p-0" align="start">
              <RangeCalendar.Root
                bind:value={dateRange}
                placeholder={dateRange?.start}
                initialFocus
                numberOfMonths={2}
                maxValue={today(getLocalTimeZone())}>
                {#snippet children({ months, weekdays })}
                  <RangeCalendar.Header>
                    <RangeCalendar.PrevButton />
                    <RangeCalendar.Heading />
                    <RangeCalendar.NextButton />
                  </RangeCalendar.Header>
                  <RangeCalendar.Months>
                    {#each months as month}
                      <RangeCalendar.Grid>
                        <RangeCalendar.GridHead>
                          <RangeCalendar.GridRow class="flex">
                            {#each weekdays as weekday}
                              <RangeCalendar.HeadCell>
                                {weekday.slice(0, 2)}
                              </RangeCalendar.HeadCell>
                            {/each}
                          </RangeCalendar.GridRow>
                        </RangeCalendar.GridHead>
                        <RangeCalendar.GridBody>
                          {#each month.weeks as weekDates}
                            <RangeCalendar.GridRow class="mt-2 w-full">
                              {#each weekDates as date}
                                <RangeCalendar.Cell {date}>
                                  <RangeCalendar.Day {date} month={month.value} />
                                </RangeCalendar.Cell>
                              {/each}
                            </RangeCalendar.GridRow>
                          {/each}
                        </RangeCalendar.GridBody>
                      </RangeCalendar.Grid>
                    {/each}
                  </RangeCalendar.Months>
                {/snippet}
              </RangeCalendar.Root>
            </Popover.Content>
          </Popover.Root>

          <!-- Job Type Select -->
          <Select.Root
            type="single"
            value={selectedJobType}
            onValueChange={(value: string | undefined) => (selectedJobType = value || '')}>
            <Select.Trigger class="w-40">
              <Select.Value placeholder="Job Type" />
            </Select.Trigger>
            <Select.Content>
              <Select.Item value="">Job Type</Select.Item>
              <Select.Item value="Full-time">Full-time</Select.Item>
              <Select.Item value="Part-time">Part-time</Select.Item>
              <Select.Item value="Contract">Contract</Select.Item>
              <Select.Item value="Freelance">Freelance</Select.Item>
              <Select.Item value="Internship">Internship</Select.Item>
            </Select.Content>
          </Select.Root>

          <!-- Status Select -->
          <Select.Root
            type="single"
            value={selectedStatus}
            onValueChange={(value: string | undefined) => (selectedStatus = value || '')}>
            <Select.Trigger class="w-32">
              <Select.Value placeholder="Status" />
            </Select.Trigger>
            <Select.Content>
              <Select.Item value="">Status</Select.Item>
              <Select.Item value="Applied">Applied</Select.Item>
              <Select.Item value="Interview">Interview</Select.Item>
              <Select.Item value="Assessment">Assessment</Select.Item>
              <Select.Item value="Offer">Offer</Select.Item>
              <Select.Item value="Rejected">Rejected</Select.Item>
            </Select.Content>
          </Select.Root>
        </div>

        <!-- Right side: Actions -->
        <div class="flex items-center gap-2">
          <!-- Columns button for both views -->
          <ColumnVisibility {tableModel} {viewMode} bind:kanbanColumnVisibility />

          <!-- Export/Import -->
          <Button
            variant="outline"
            size="sm"
            class="h-8"
            onclick={exportCSV}
            disabled={isExporting}>
            <Download class={`mr-2 h-4 w-4 ${isExporting ? 'animate-pulse' : ''}`} />
            {isExporting ? 'Exporting...' : 'Export CSV'}
          </Button>

          <Button
            variant="outline"
            size="sm"
            class="h-8"
            onclick={handleImportCSV}
            disabled={isImporting}>
            <Upload class={`mr-2 h-4 w-4 ${isImporting ? 'animate-pulse' : ''}`} />
            {isImporting ? 'Importing...' : 'Import CSV'}
          </Button>
        </div>
      </div>
    </div>
    {#if applications.length === 0}
      <div class="rounded-lg border p-6">
        <div class="text-center">
          <Briefcase class="text-muted-foreground mx-auto h-12 w-12" />
          <h3 class="mt-4 text-lg font-medium">No applications yet</h3>
          <p class="text-muted-foreground mt-2">
            Start tracking your job applications by adding them manually or applying to jobs through
            our platform.
          </p>
          <Button onclick={() => (newJobModalOpen = true)} class="mt-4">
            <Plus class="mr-2 h-4 w-4" />
            Add Your First Application
          </Button>
        </div>
      </div>
    {:else if filteredApplications.length === 0}
      <div class="rounded-lg border p-6">
        <div class="text-center">
          <Search class="text-muted-foreground mx-auto h-12 w-12" />
          <h3 class="mt-4 text-lg font-medium">No applications found</h3>
          <p class="text-muted-foreground mt-2">
            No applications match your search criteria. Try adjusting your search terms.
          </p>
          <Button variant="outline" onclick={() => (currentFilters.searchTerm = '')} class="mt-4">
            Clear Search
          </Button>
        </div>
      </div>
    {:else if viewMode === 'kanban'}
      <!-- Kanban Board -->
      <KanbanView
        {columns}
        {groupedApplications}
        {openApplicationDetails}
        selectedItems={selectedKanbanItems}
        onSelectionChange={handleKanbanSelectionChange}
        columnVisibility={kanbanColumnVisibility}
        on:finalize={handleKanbanFinalize} />
    {:else}
      <!-- List View with Data Table -->
      <DataTable data={filteredApplications} {openApplicationDetails} bind:tableModel />
    {/if}
  </Tabs.Content>

  <!-- Archived Applications Tab -->
  <Tabs.Content value="archived" class="p-4">
    <!-- Header with actions -->
    <div class="mb-6 flex items-center justify-between">
      <!-- Results -->
      <div class="flex items-center gap-4">
        <Badge variant="secondary" class="text-sm">
          {applications.filter((app) => app.status === 'Rejected' || app.status === 'Offer').length}
          archived
        </Badge>
      </div>

      <!-- Search -->
      <div class="relative">
        <Search class="text-muted-foreground absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2" />
        <Input
          type="text"
          placeholder="Search archived applications..."
          class="w-64 pl-9"
          bind:value={currentFilters.searchTerm} />
      </div>
    </div>

    {#if applications.filter((app) => app.status === 'Rejected' || app.status === 'Offer').length === 0}
      <div class="rounded-lg border p-6">
        <div class="text-center">
          <CheckCircle class="text-muted-foreground mx-auto h-12 w-12" />
          <h3 class="mt-4 text-lg font-medium">No archived applications</h3>
          <p class="text-muted-foreground mt-2">
            Applications that are rejected or result in offers will appear here.
          </p>
        </div>
      </div>
    {:else}
      <!-- Archived Applications List -->
      <div class="space-y-4">
        <DataTable data={filteredApplications} {openApplicationDetails} />
      </div>
    {/if}
  </Tabs.Content>
</Tabs.Root>

<!-- Application Details Sheet -->
<ApplicationDetailsSheet bind:sheetOpen {selectedApplication} {statusColors} />

<!-- New Job Modal -->
<AddJobModal
  bind:open={newJobModalOpen}
  {form}
  {errors}
  {constraints}
  {submitting}
  {enhance}
  {reset}
  {jobTypes}
  {jobStatuses} />
