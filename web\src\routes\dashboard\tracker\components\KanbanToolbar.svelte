<script lang="ts">
  import { Button } from '$lib/components/ui/button';
  import { Input } from '$lib/components/ui/input';
  import * as DropdownMenu from '$lib/components/ui/dropdown-menu';
  import { Badge } from '$lib/components/ui/badge';
  import DataTableFacetedFilter from '../data-table-faceted-filter.svelte';
  import DataTableViewOptions from '../data-table-view-options.svelte';
  import { statuses, jobTypes } from '../types';
  import { 
    Search, 
    Download, 
    Upload, 
    Archive, 
    Trash2, 
    MoreHorizontal,
    RefreshCw
  } from 'lucide-svelte';
  import { toast } from 'svelte-sonner';

  export let data: any[] = [];
  export let searchTerm = '';
  export let selectedItems: Set<string> = new Set();
  export let onBulkAction: (action: string, status?: string) => void = () => {};

  // Local state for filters
  let statusFilter: string[] = [];
  let jobTypeFilter: string[] = [];
  let isExporting = false;
  let isImporting = false;

  // Format options for filters
  const statusOptions = statuses.map((status) => ({
    value: status.id,
    label: status.name,
    icon: status.icon,
  }));

  const jobTypeOptions = jobTypes.map((type) => ({
    value: type.id,
    label: type.name,
    icon: type.icon,
  }));

  // Count applications by status and job type
  $: statusCounts = statusOptions.reduce((acc, status) => {
    acc[status.value] = data.filter((app) => app.status === status.value).length;
    return acc;
  }, {});

  $: jobTypeCounts = jobTypeOptions.reduce((acc, type) => {
    acc[type.value] = data.filter((app) => app.jobType === type.value).length;
    return acc;
  }, {});

  $: selectedCount = selectedItems.size;

  // Filter handlers
  function handleStatusFilterChange(values: string[]) {
    statusFilter = values;
    // Emit filter change event or handle filtering logic here
  }

  function handleJobTypeFilterChange(values: string[]) {
    jobTypeFilter = values;
    // Emit filter change event or handle filtering logic here
  }

  // Bulk action handlers
  function handleBulkArchive() {
    if (selectedCount === 0) {
      toast.error('Please select applications to archive');
      return;
    }
    onBulkAction('archive');
    toast.success(`Archived ${selectedCount} applications`);
  }

  function handleBulkDelete() {
    if (selectedCount === 0) {
      toast.error('Please select applications to delete');
      return;
    }
    onBulkAction('delete');
    toast.success(`Deleted ${selectedCount} applications`);
  }

  function handleBulkStatusUpdate(status: string) {
    if (selectedCount === 0) {
      toast.error('Please select applications to update');
      return;
    }
    onBulkAction('updateStatus', status);
    toast.success(`Updated ${selectedCount} applications to ${status}`);
  }

  // Export CSV
  async function exportCSV() {
    if (isExporting) return;
    isExporting = true;
    
    try {
      const headers = ['Company', 'Position', 'Location', 'Applied Date', 'Status', 'Job Type', 'Next Action'];
      const csvContent = [
        headers.join(','),
        ...data.map((app) =>
          [
            `"${app.company?.replace(/"/g, '""') || ''}"`,
            `"${app.position?.replace(/"/g, '""') || ''}"`,
            `"${app.location?.replace(/"/g, '""') || ''}"`,
            `"${app.appliedDate?.replace(/"/g, '""') || ''}"`,
            `"${app.status?.replace(/"/g, '""') || ''}"`,
            `"${app.jobType?.replace(/"/g, '""') || ''}"`,
            `"${app.nextAction?.replace(/"/g, '""') || ''}"`,
          ].join(',')
        ),
      ].join('\n');

      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.setAttribute('href', url);
      link.setAttribute('download', 'job_applications.csv');
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      toast.success('CSV exported successfully!');
    } catch (error) {
      toast.error('Failed to export CSV');
    } finally {
      setTimeout(() => {
        isExporting = false;
      }, 500);
    }
  }

  // Import CSV
  function handleImportCSV() {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.csv';
    input.onchange = (e) => {
      const file = (e.target as HTMLInputElement)?.files?.[0];
      if (file) {
        isImporting = true;
        // Here you would handle the CSV import
        setTimeout(() => {
          isImporting = false;
          toast.success('CSV imported successfully!');
        }, 1000);
      }
    };
    input.click();
  }
</script>

<div class="space-y-4">
  <!-- Top row: Search, filters, and actions -->
  <div class="flex items-center justify-between">
    <!-- Left side: Search and filters -->
    <div class="flex items-center gap-2">
      <!-- Search -->
      <div class="relative">
        <Search class="text-muted-foreground absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2" />
        <Input
          type="text"
          placeholder="Search for roles or companies..."
          class="w-64 pl-9"
          bind:value={searchTerm} />
      </div>

      <!-- Filters -->
      <div class="flex items-center gap-2">
        <!-- Applied from -->
        <Input type="date" placeholder="Applied from" class="w-40" />
        
        <!-- Applied until -->
        <Input type="date" placeholder="Applied until" class="w-40" />
        
        <!-- Job Type Filter -->
        <DataTableFacetedFilter
          title="Job Type"
          options={jobTypeOptions}
          filterValues={jobTypeFilter}
          counts={jobTypeCounts}
          onFilterChange={handleJobTypeFilterChange} />

        <!-- Status Filter -->
        <DataTableFacetedFilter
          title="Status"
          options={statusOptions}
          filterValues={statusFilter}
          counts={statusCounts}
          onFilterChange={handleStatusFilterChange} />
      </div>
    </div>

    <!-- Right side: Export/Import -->
    <div class="flex items-center gap-2">
      <Button
        variant="outline"
        size="sm"
        onclick={exportCSV}
        disabled={isExporting}
        class="h-8">
        <Download class={`mr-2 h-4 w-4 ${isExporting ? 'animate-pulse' : ''}`} />
        {isExporting ? 'Exporting...' : 'Export CSV'}
      </Button>
      
      <Button
        variant="outline"
        size="sm"
        onclick={handleImportCSV}
        disabled={isImporting}
        class="h-8">
        <Upload class={`mr-2 h-4 w-4 ${isImporting ? 'animate-pulse' : ''}`} />
        {isImporting ? 'Importing...' : 'Import CSV'}
      </Button>
    </div>
  </div>

  <!-- Bottom row: Bulk actions -->
  {#if selectedCount > 0}
    <div class="flex items-center justify-between">
      <!-- Left side: Selection info and bulk actions -->
      <div class="flex items-center gap-2">
        <Badge variant="secondary" class="text-sm">
          {selectedCount} Jobs Selected
        </Badge>
        
        <!-- Bulk Actions Dropdown -->
        <DropdownMenu.Root>
          <DropdownMenu.Trigger>
            <Button variant="outline" size="sm" class="h-8">
              <MoreHorizontal class="mr-2 h-4 w-4" />
              Actions
            </Button>
          </DropdownMenu.Trigger>
          <DropdownMenu.Content align="start">
            <DropdownMenu.Item onclick={handleBulkArchive}>
              <Archive class="mr-2 h-4 w-4" />
              Archive
            </DropdownMenu.Item>
            <DropdownMenu.Item onclick={handleBulkDelete} class="text-destructive">
              <Trash2 class="mr-2 h-4 w-4" />
              Delete
            </DropdownMenu.Item>
            <DropdownMenu.Separator />
            <DropdownMenu.Sub>
              <DropdownMenu.SubTrigger>
                <RefreshCw class="mr-2 h-4 w-4" />
                Update Status
              </DropdownMenu.SubTrigger>
              <DropdownMenu.SubContent>
                {#each statuses as status}
                  <DropdownMenu.Item onclick={() => handleBulkStatusUpdate(status.id)}>
                    {@const IconComponent = status.icon}
                    <IconComponent class="mr-2 h-4 w-4" />
                    {status.name}
                  </DropdownMenu.Item>
                {/each}
              </DropdownMenu.SubContent>
            </DropdownMenu.Sub>
          </DropdownMenu.Content>
        </DropdownMenu.Root>
      </div>
    </div>
  {/if}
</div>
